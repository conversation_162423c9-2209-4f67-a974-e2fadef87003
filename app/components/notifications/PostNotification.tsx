import type { PostNotification } from "~/lib/stream/types";
import { AvatarGroup } from "./AvatarGroup";
import { ImageGrid } from "./ImageGrid";
import { BaseCard } from "./BaseCard";

interface PostNotificationProps {
  notification: PostNotification;
  onMarkRead?: (id: string) => void;
}

export function PostNotification({
  notification,
  onMarkRead,
}: PostNotificationProps) {
  const { actors, post, activityCount } = notification;

  const handleClick = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  const getPostText = () => {
    if (activityCount === 1) {
      return "posted";
    } else {
      return `made ${activityCount} posts`;
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onClick={handleClick}>
      <AvatarGroup urls={actors.avatars} />

      <div className="flex-1 min-w-0">
        <p className="text-sm text-white mb-2">
          <span className="font-medium">{getActorText()}</span>{" "}
          <span className="text-zinc-400">{getPostText()}</span>
        </p>

        {post.message && (
          <p className="text-sm text-zinc-300 mb-2 line-clamp-2">
            "{post.message}"
          </p>
        )}

        {post.images && post.images.length > 0 && (
          <ImageGrid images={post.images} className="mb-2" />
        )}

        <div className="flex items-center justify-between">
          <span className="text-xs text-zinc-500">
            {formatTimeAgo(notification.createdAt)}
          </span>
        </div>
      </div>
    </BaseCard>
  );
}

function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h`;
  }
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d`;
  }

  return new Date(dateString).toLocaleDateString();
}
