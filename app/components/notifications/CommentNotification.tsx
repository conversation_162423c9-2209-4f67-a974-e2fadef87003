import { MessageCircle } from "lucide-react";
import type { CommentNotification } from "~/lib/stream/types";
import { AvatarGroup } from "./AvatarGroup";
import { ImageGrid } from "./ImageGrid";
import { BaseCard } from "./BaseCard";

interface CommentNotificationProps {
  notification: CommentNotification;
  onMarkRead?: (id: string) => void;
}

export function CommentNotification({
  notification,
  onMarkRead,
}: CommentNotificationProps) {
  const { actors, targetPost, commentPreview } = notification;

  const handleClick = () => {
    if (!notification.isRead && onMarkRead) {
      onMarkRead(notification.id);
    }
  };

  const getActorText = () => {
    if (actors.count === 1) {
      return actors.names[0];
    } else if (actors.count === 2) {
      return `${actors.names[0]} and ${actors.names[1]}`;
    } else {
      return `${actors.names[0]} and ${actors.count - 1} others`;
    }
  };

  const getCommentText = () => {
    if (actors.count === 1) {
      return "commented on your post";
    } else {
      return "commented on your post";
    }
  };

  return (
    <BaseCard isRead={notification.isRead} onClick={handleClick}>
      <div className="relative">
        <AvatarGroup urls={actors.avatars} />
        <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center border-2 border-black">
          <MessageCircle className="w-3 h-3 text-white fill-current" />
        </div>
      </div>

      <div className="flex-1 min-w-0">
        <p className="text-sm text-white mb-2">
          <span className="font-medium">{getActorText()}</span>{" "}
          <span className="text-zinc-400">{getCommentText()}</span>
        </p>

        {commentPreview && (
          <div className="bg-zinc-800 rounded-lg p-2 mb-2">
            <p className="text-sm text-zinc-300 italic">"{commentPreview}"</p>
          </div>
        )}

        <div className="flex items-start gap-3">
          <div className="flex-1">
            {targetPost.message && (
              <p className="text-sm text-zinc-400 mb-2 line-clamp-2">
                "{targetPost.message}"
              </p>
            )}

            <div className="flex items-center justify-between">
              <span className="text-xs text-zinc-500">
                {formatTimeAgo(notification.createdAt)}
              </span>
            </div>
          </div>

          {targetPost.images && targetPost.images.length > 0 && (
            <ImageGrid images={targetPost.images} />
          )}
        </div>
      </div>
    </BaseCard>
  );
}

function formatTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) return "Just now";
  if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m`;
  }
  if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h`;
  }
  if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d`;
  }

  return new Date(dateString).toLocaleDateString();
}
