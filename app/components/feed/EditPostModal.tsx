import { useState, useCallback, useEffect } from "react";
import { X, <PERSON>I<PERSON>, Loader2, Check, ZoomIn } from "lucide-react";
import type { EnrichedActivityWithText } from "./FeedPostCard";
import { useGetUploadUrl } from "~/lib/api/client-queries";

interface EditPostModalProps {
  activity: EnrichedActivityWithText;
  isOpen: boolean;
  onClose: () => void;
  onSave: (
    activityId: string,
    text: string,
    attachment?: {
      type: string;
      url: string;
      fileName?: string;
    }
  ) => Promise<void>;
}

// Helper function to check if a file is an image
const isImageFile = (file: File): boolean => {
  return file.type.startsWith("image/");
};

// Helper function to get activity content
const getActivityContent = (activity: EnrichedActivityWithText): string => {
  if (activity.message) return activity.message;
  if (activity.text) return activity.text;
  if (typeof activity.object === "string") return activity.object;
  const obj = activity.object as any;
  if (obj?.text) return obj.text;
  if (obj?.content) return obj.content;
  if (obj?.id) return `${activity.verb} ${obj.id}`;
  return `${activity.verb}`;
};

// Helper function to get existing image from activity
const getExistingImage = (
  activity: EnrichedActivityWithText
): string | null => {
  // Check legacy image field
  if (activity.image) return activity.image;

  // Check attachments for image
  if (activity.attachments && activity.attachments.length > 0) {
    const imageAttachment = activity.attachments.find(
      (att) => att.type === "image" && att.image_url
    );
    if (imageAttachment?.image_url) return imageAttachment.image_url;
  }

  return null;
};

export function EditPostModal({
  activity,
  isOpen,
  onClose,
  onSave,
}: EditPostModalProps) {
  const [text, setText] = useState("");
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewImage, setPreviewImage] = useState<string | null>(null);
  const [existingImage, setExistingImage] = useState<string | null>(null);
  const [isSaving, setIsSaving] = useState(false);
  const [showImagePreview, setShowImagePreview] = useState(false);
  const [previewImageUrl, setPreviewImageUrl] = useState("");

  const getUploadUrl = useGetUploadUrl();

  // Initialize form data when modal opens
  useEffect(() => {
    if (isOpen && activity) {
      setText(getActivityContent(activity));
      const existingImg = getExistingImage(activity);
      setExistingImage(existingImg);
      setSelectedFile(null);
      setPreviewImage(null);
    }
  }, [isOpen, activity]);

  // Handle escape key
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && !isSaving) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, isSaving, onClose]);

  const handleImageUpload = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const file = e.target.files?.[0];
      if (file) {
        if (!isImageFile(file)) {
          alert("Please select an image file");
          return;
        }

        const maxSize = 100 * 1024 * 1024; // 100MB
        if (file.size > maxSize) {
          alert("File size must be less than 100MB");
          return;
        }

        setSelectedFile(file);

        const reader = new FileReader();
        reader.onloadend = () => {
          setPreviewImage(reader.result as string);
        };
        reader.readAsDataURL(file);
      }
      e.target.value = "";
    },
    []
  );

  const handleRemoveImage = useCallback(() => {
    setSelectedFile(null);
    setPreviewImage(null);
    setExistingImage(null);
  }, []);

  const handleImageClick = useCallback((imageUrl: string) => {
    setPreviewImageUrl(imageUrl);
    setShowImagePreview(true);
  }, []);

  const handleSave = useCallback(async () => {
    if (!text.trim()) return;

    setIsSaving(true);
    try {
      let attachment = undefined;

      if (selectedFile) {
        // Upload new image
        const uploadUrlResponse = await getUploadUrl.mutateAsync({
          contentType: selectedFile.type,
          fileName: selectedFile.name,
        });

        const uploadResponse = await fetch(uploadUrlResponse.uploadUrl, {
          method: "PUT",
          headers: {
            "Content-Type": selectedFile.type,
          },
          body: selectedFile,
        });

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.statusText}`);
        }

        attachment = {
          type: "image",
          url: uploadUrlResponse.cdnUrl,
          fileName: selectedFile.name,
        };
      } else if (existingImage) {
        // Keep existing image
        attachment = {
          type: "image",
          url: existingImage,
        };
      }

      await onSave(activity.id, text.trim(), attachment);
      onClose();
    } catch (error) {
      console.error("Error saving post:", error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Failed to save post. Please try again.";

      // Show a more user-friendly dialog for WIP message
      if (errorMessage.includes("🚧") || errorMessage.includes("coming soon")) {
        alert(errorMessage);
      } else {
        alert(`Failed to save post: ${errorMessage}`);
      }
    } finally {
      setIsSaving(false);
    }
  }, [
    text,
    selectedFile,
    existingImage,
    getUploadUrl,
    onSave,
    activity.id,
    onClose,
  ]);

  if (!isOpen) return null;

  const currentImage = previewImage || existingImage;

  return (
    <>
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50"
        onClick={!isSaving ? onClose : undefined}
      >
        <div
          className="bg-zinc-900 rounded-lg border border-zinc-700 w-full max-w-2xl mx-4 max-h-[90vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-zinc-700">
            <h2 className="text-xl font-semibold text-white">Edit Post</h2>
            <button
              onClick={onClose}
              disabled={isSaving}
              className="text-zinc-400 hover:text-white transition-colors disabled:opacity-50"
            >
              <X className="w-6 h-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto p-6 space-y-4">
            {/* Text Input */}
            <textarea
              value={text}
              onChange={(e) => setText(e.target.value)}
              placeholder="What's on your mind?"
              className="w-full px-4 py-3 text-white bg-zinc-700 border border-zinc-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none placeholder-zinc-400"
              rows={4}
              disabled={isSaving}
            />

            {/* Image Preview */}
            {currentImage && (
              <div className="relative group">
                <img
                  src={currentImage}
                  alt="Post image"
                  className="max-h-64 rounded-lg cursor-pointer transition-opacity group-hover:opacity-90"
                  onClick={() => handleImageClick(currentImage)}
                />
                <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity bg-black/20 rounded-lg pointer-events-none">
                  <ZoomIn className="w-8 h-8 text-white" />
                </div>
                <button
                  onClick={handleRemoveImage}
                  disabled={isSaving}
                  className="absolute top-2 right-2 p-1 bg-black/50 rounded-full hover:bg-black/70 transition-colors disabled:opacity-50"
                >
                  <X className="w-4 h-4 text-white" />
                </button>
              </div>
            )}

            {/* Image Upload */}
            <div className="flex items-center gap-2">
              <label className="cursor-pointer">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageUpload}
                  disabled={isSaving}
                  className="hidden"
                />
                <div className="p-2 text-zinc-400 hover:bg-zinc-700 rounded-lg transition-colors">
                  <ImageIcon className="w-5 h-5" />
                </div>
              </label>
              <span className="text-sm text-zinc-400">
                {currentImage ? "Change image" : "Add image"}
              </span>
            </div>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end gap-3 p-6 border-t border-zinc-700">
            <button
              onClick={onClose}
              disabled={isSaving}
              className="px-4 py-2 text-zinc-400 hover:text-white transition-colors disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              disabled={!text.trim() || isSaving}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
            >
              {isSaving ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin" />
                  Saving...
                </>
              ) : (
                <>
                  <Check className="w-4 h-4" />
                  Save Changes
                </>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {showImagePreview && (
        <div
          className="fixed inset-0 bg-black/90 backdrop-blur-sm flex items-center justify-center z-[60]"
          onClick={() => setShowImagePreview(false)}
        >
          <div className="relative max-w-[90vw] max-h-[90vh] flex items-center justify-center">
            <img
              src={previewImageUrl}
              alt="Preview"
              className="max-w-full max-h-full object-contain rounded-lg"
              onClick={(e) => e.stopPropagation()}
            />
            <button
              onClick={() => setShowImagePreview(false)}
              className="absolute top-4 right-4 p-2 bg-black/50 rounded-full hover:bg-black/70 transition-colors"
            >
              <X className="w-6 h-6 text-white" />
            </button>
          </div>
        </div>
      )}
    </>
  );
}
