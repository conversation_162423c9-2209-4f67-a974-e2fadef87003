/**
 * Utility functions for feed operations
 */

/**
 * Extract organization ID from feedId
 * @param feedId - Feed ID in format: org-{orgId}_group-{groupId}_cohort-{cohortId}_module-{moduleId}
 * @returns Organization ID as number, or null if not found
 */
export function extractOrgIdFromFeedId(feedId: string): number | null {
  const match = feedId.match(/^org-(\d+)_/);
  return match ? parseInt(match[1], 10) : null;
}

/**
 * Check if user has permission to post to a feed
 * @param userRole - User's role (creator, moderator, etc.)
 * @param userOrgId - User's organization ID
 * @param feedId - Feed ID to check against
 * @returns Boolean indicating if user can post
 */
export function canUserPostToFeed(
  userRole: string | undefined,
  userOrgId: number | undefined,
  feedId: string
): boolean {
  // Only creators and moderators can post
  if (userRole !== "creator" && userRole !== "moderator") {
    return false;
  }

  // User must belong to the same organization as the feed
  const feedOrgId = extractOrgIdFromFeedId(feedId);
  return userOrgId === feedOrgId;
}

/**
 * Check if user can edit a specific post
 * @param currentUserId - Current user's ID
 * @param activity - The activity/post to check
 * @returns Boolean indicating if user can edit the post
 */
export function canUserEditPost(
  currentUserId: string | null,
  activity: any
): boolean {
  if (!currentUserId || !activity) {
    return false;
  }

  // User can only edit their own posts
  // Check if the actor ID matches the current user ID
  const actorId =
    typeof activity.actor === "string" ? activity.actor : activity.actor?.id;

  return actorId === currentUserId;
}
