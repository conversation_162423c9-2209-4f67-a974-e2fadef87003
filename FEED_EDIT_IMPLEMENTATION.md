# Feed Post Edit Implementation

## Overview

This implementation adds the ability for users to edit their own feed posts using GetStream's updateActivities functionality. Posts can be edited in a modal popup with support for both text and image editing.

## Key Changes

### 1. Modified `useFeedPost` Hook

- Added `foreign_id` to new posts to enable future updates
- Uses format: `post:${userId}:${timestamp}` for uniqueness

### 2. New `useFeedEditPost` Hook

- Handles updating existing posts via GetStream's `updateActivities` method
- Requires `foreign_id`, `time`, and `activityId` to update posts
- Supports both text and image attachment updates
- Includes optimistic updates for better UX

### 3. New `EditPostModal` Component

- Modal popup for editing posts with better UX
- Supports text editing with textarea
- Supports image upload, replacement, and removal
- Image preview with zoom functionality
- Follows existing modal patterns in the codebase

### 4. Updated `FeedPostCard` Component

- Shows edit button in dropdown menu for post owners only
- Launches edit modal instead of inline editing
- Includes permission checking via `canUserEditPost` utility

### 5. Permission System

- Users can only edit their own posts
- Checks if `currentUserId` matches the post's `actor.id`
- Edit button only appears for post owners

## Files Modified

1. **`app/lib/api/client-queries.ts`**

   - Modified `useFeedPost` to include `foreign_id`
   - Added `useFeedEditPost` hook
   - Added export for new hook

2. **`app/lib/utils/feed.ts`**

   - Added `canUserEditPost` utility function

3. **`app/components/feed/EditPostModal.tsx`** _(NEW)_

   - Modal component for editing posts
   - Text and image editing capabilities
   - Image upload, preview, and removal
   - Follows existing modal patterns

4. **`app/components/feed/FeedPostCard.tsx`**

   - Replaced inline editing with modal
   - Added dropdown menu with edit option
   - Added permission checking
   - Integrated EditPostModal component

5. **`app/components/modules/FeedModule.tsx`**

   - Added edit mutation and handler with image support
   - Updated FeedPostCard props to include edit functionality

6. **`app/routes/groups/[groupId]/cohorts/[cohortId]/modules/[moduleId]/posts/[activityId].tsx`**

   - Added edit functionality to individual post page with image support

## Usage

1. **Creating Posts**: Posts now automatically include a `foreign_id` for future editing
2. **Editing Posts**: Users see an edit button (three dots menu) on their own posts
3. **Edit Modal**: Clicking edit opens a modal popup with:
   - Text editing with textarea
   - Image upload/replacement/removal
   - Image preview with zoom functionality
   - Save/Cancel buttons
4. **Permissions**: Only post authors can edit their posts
5. **Image Handling**: Users can:
   - Add images to text-only posts
   - Replace existing images
   - Remove images entirely
   - Preview images before saving

## Technical Notes

- GetStream requires both `foreign_id` and `time` fields to update activities
- The `updateActivities` method is used instead of creating new posts
- Optimistic updates provide immediate feedback while the server processes the change
- All existing posts without `foreign_id` cannot be edited (this is expected)

## Future Enhancements

- Add image editing capability
- Add delete post functionality
- Add edit history/timestamps
- Add admin override permissions
