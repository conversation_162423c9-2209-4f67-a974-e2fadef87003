# Feed Post Edit Implementation

## Overview
This implementation adds the ability for users to edit their own feed posts using GetStream's updateActivities functionality.

## Key Changes

### 1. Modified `useFeedPost` Hook
- Added `foreign_id` to new posts to enable future updates
- Uses format: `post:${userId}:${timestamp}` for uniqueness

### 2. New `useFeedEditPost` Hook
- Handles updating existing posts via GetStream's `updateActivities` method
- Requires `foreign_id`, `time`, and `activityId` to update posts
- Includes optimistic updates for better UX

### 3. Updated `FeedPostCard` Component
- Added edit functionality with inline editing form
- Shows edit button in dropdown menu for post owners only
- Includes permission checking via `canUserEditPost` utility

### 4. Permission System
- Users can only edit their own posts
- Checks if `currentUserId` matches the post's `actor.id`
- Edit button only appears for post owners

## Files Modified

1. **`app/lib/api/client-queries.ts`**
   - Modified `useFeedPost` to include `foreign_id`
   - Added `useFeedEditPost` hook
   - Added export for new hook

2. **`app/lib/utils/feed.ts`**
   - Added `canUserEditPost` utility function

3. **`app/components/feed/FeedPostCard.tsx`**
   - Added edit state management
   - Added edit form UI
   - Added dropdown menu with edit option
   - Added permission checking

4. **`app/components/modules/FeedModule.tsx`**
   - Added edit mutation and handler
   - Updated FeedPostCard props to include edit functionality

5. **`app/routes/groups/[groupId]/cohorts/[cohortId]/modules/[moduleId]/posts/[activityId].tsx`**
   - Added edit functionality to individual post page

## Usage

1. **Creating Posts**: Posts now automatically include a `foreign_id` for future editing
2. **Editing Posts**: Users see an edit button (three dots menu) on their own posts
3. **Edit Form**: Clicking edit shows an inline textarea with save/cancel buttons
4. **Permissions**: Only post authors can edit their posts

## Technical Notes

- GetStream requires both `foreign_id` and `time` fields to update activities
- The `updateActivities` method is used instead of creating new posts
- Optimistic updates provide immediate feedback while the server processes the change
- All existing posts without `foreign_id` cannot be edited (this is expected)

## Future Enhancements

- Add image editing capability
- Add delete post functionality
- Add edit history/timestamps
- Add admin override permissions
